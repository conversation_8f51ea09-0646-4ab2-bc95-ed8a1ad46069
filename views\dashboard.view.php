<?php
#region region DOCS

/** @var Puesto[] $puestos */

/** @var Empleado[] $empleados */

use App\classes\Puesto;
use App\classes\Empleado;
use App\classes\EmpleadoTurno;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Dashboard</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>

	<!-- Estilos adicionales -->
	<link href="<?php echo RUTA ?>resources/css/dashboard_style.css" rel="stylesheet" />
	<link href="<?php echo RUTA ?>resources/adm_assets/plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet" />

	<!-- Custom CSS for scheduled appointments modal -->
	<style>
		/* Darker background for scheduled appointments table body */
		#modal-citas-programadas .table tbody {
			background-color: #2d353c;
		}

		/* Center align action buttons */
		#modal-citas-programadas .table .text-center {
			text-align: center !important;
		}

		/* Ensure proper contrast for table text */
		#modal-citas-programadas .table tbody td {
			color: #fff;
			border-color: #495057;
		}

		/* Style for action buttons */
		#modal-citas-programadas .btn-group .btn {
			margin: 0 2px;
		}

		/* Loading overlay for services container */
		#servicios-loading-overlay {
			backdrop-filter: blur(2px);
			transition: opacity 0.3s ease-in-out;
			border: 1px solid #495057;
		}

		#servicios-loading-overlay .spinner-border {
			width: 3rem;
			height: 3rem;
			border-width: 0.3em;
		}

		/* Ensure services container has proper positioning */
		#servicios-container {
			position: relative !important;
			min-height: 200px;
		}
	</style>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content content-dashboard-citas">
		<?php #region region TURNOS SECTION ?>
		<div class="panel panel-inverse">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title fs-18px">Gestión de Turnos</h4>
				<div class="mb-auto d-flex gap-2">
					<button type="button" class="btn btn-primary" id="btn-nueva-cita">
						<i class="fa fa-calendar-plus me-1"></i> Nueva Cita
					</button>
					<button type="button" class="btn btn-info" id="btn-ver-citas-programadas">
						<i class="fa fa-calendar-check me-1"></i> Ver Citas Programadas
					</button>
					<button type="button" class="btn btn-warning" id="btn-cerrar-caja">
						<i class="fa fa-cash-register me-1"></i> Cerrar Caja
					</button>
				</div>
			</div>
			<div class="panel-body">
				<div class="row">
					<?php if (empty($puestos)): ?>
						<div class="col-12">
							<div class="alert alert-warning">
								No hay puestos activos disponibles. <a href="puestos" class="alert-link">Crear nuevos puestos</a>.
							</div>
						</div>
					<?php else: ?>
						<?php foreach ($puestos as $puesto): ?>
							<div class="col-md-4 col-sm-6 mb-4">
								<div class="card position-card <?php echo isset($puesto->turno_activo) && $puesto->turno_activo ? 'occupied' : 'available'; ?>" style="position: relative;">
									<div class="card-header d-flex justify-content-between align-items-center bg-dark text-white">
										<span class="titulo-puesto">
											<?php echo htmlspecialchars($puesto->getDescripcion()); ?>
											<span class="position-status <?php echo isset($puesto->turno_activo) && $puesto->turno_activo ? 'status-occupied' : 'status-available'; ?>">
												<?php echo isset($puesto->turno_activo) && $puesto->turno_activo ? 'Online' : 'Offline'; ?>
											</span>
											<?php if (isset($puesto->turno_activo->tiene_cita_activa) && $puesto->turno_activo && $puesto->turno_activo->tiene_cita_activa): ?>
												<div class="appointment-indicator" title="Tiene cita activa">
													<i class="fa fa-calendar-check"></i>
												</div>
											<?php endif; ?>
										</span>
									</div>
									<div class="card-body">
										<?php if (isset($puesto->turno_activo) && $puesto->turno_activo): ?>
											<div class="employee-badge w-100">
												<i class="fa fa-user me-2"></i>
												<?php echo htmlspecialchars($puesto->turno_activo->getNombre_empleado()); ?>
											</div>
											<p class="mb-1">
												<strong>Inicio del turno:</strong><br>
												<?php echo date('Y-m-d h:i A', strtotime($puesto->turno_activo->getFecha_inicio())); ?>
											</p>
											<?php if (isset($puesto->turno_activo->tiene_cita_activa) && $puesto->turno_activo->tiene_cita_activa && isset($puesto->turno_activo->cita_activa)): ?>
												<div class="appointment-panel">
													<div class="appointment-panel-header">
														<span><i class="fa fa-calendar-check me-1"></i> Cita <span class="ms-2"><?php echo date('H:i', strtotime($puesto->turno_activo->cita_activa->getFecha_inicio())); ?></span></span>
														<div>
															<button type="button" class="btn btn-xs btn-warning btn-editar-cita p-0" title="Editar cita"
																	data-id-cita="<?php echo $puesto->turno_activo->cita_activa->getId(); ?>">
																<i class="fa fa-edit"></i>
															</button>

														</div>

													</div>
													<div class="appointment-panel-body">
														<?php if (isset($puesto->turno_activo->servicios_cita) && !empty($puesto->turno_activo->servicios_cita)): ?>
															<ul class="appointment-service-list">
																<?php foreach ($puesto->turno_activo->servicios_cita as $servicio): ?>
																	<li class="appointment-service-item">
																		<span><?php echo htmlspecialchars($servicio->getDescripcion()); ?></span>
																		<span class="appointment-service-price">$<?php echo number_format($servicio->getValor(), 0, ',', '.'); ?></span>
																	</li>
																<?php endforeach; ?>
															</ul>
															<div class="appointment-total">
																<span>Total:</span>
																<span class="appointment-total-price">$<?php echo number_format($puesto->turno_activo->total_servicios, 0, ',', '.'); ?></span>
															</div>
														<?php else: ?>
															<p class="text-muted mb-0">No hay servicios asociados a esta cita.</p>
														<?php endif; ?>
													</div>
												</div>
												<?php if (isset($puesto->turno_activo->cita_activa)): ?>
												<div class="mt-2 mb-2">
													<div class="d-flex gap-2">
														<button type="button" class="btn btn-sm btn-success btn-finalizar-cita flex-grow-1"
																data-id-cita="<?php echo $puesto->turno_activo->cita_activa->getId(); ?>">
															<i class="fa fa-check-circle me-1"></i> Finalizar Cita
														</button>
														<button type="button" class="btn btn-sm btn-danger btn-cancelar-cita flex-grow-1"
																data-id-cita="<?php echo $puesto->turno_activo->cita_activa->getId(); ?>">
															<i class="fa fa-times-circle me-1"></i> Cancelar Cita
														</button>
													</div>
												</div>
												<?php endif; ?>
											<?php endif; ?>
											<?php if (isset($puesto->turno_activo->tiene_cita_activa) && $puesto->turno_activo->tiene_cita_activa && isset($puesto->turno_activo->cita_activa)): ?>
												<button type="button" class="btn btn-danger btn-sm w-100" disabled
												        data-bs-toggle="tooltip" data-bs-placement="top"
												        title="No se puede finalizar el turno mientras hay una cita activa">
													<i class="fa fa-times-circle me-1"></i> Finalizar Turno
												</button>
											<?php else: ?>
												<button type="button" class="btn btn-danger btn-sm btn-finalizar-turno w-100"
												        data-id-turno="<?php echo $puesto->turno_activo->getId(); ?>"
												        data-id-puesto="<?php echo $puesto->getId(); ?>"
												        data-descripcion-puesto="<?php echo htmlspecialchars($puesto->getDescripcion()); ?>">
													<i class="fa fa-times-circle me-1"></i> Finalizar Turno
												</button>
											<?php endif; ?>
										<?php else: ?>
											<p class="mb-3">No hay empleado asignado actualmente a este puesto.</p>
											<?php if (!empty($empleados)): ?>
												<div class="form-group mb-3">
													<label for="empleado-<?php echo $puesto->getId(); ?>" class="form-label">Asignar empleado disponible:</label>
													<select class="form-select" id="empleado-<?php echo $puesto->getId(); ?>" data-id-puesto="<?php echo $puesto->getId(); ?>">
														<option value="">Seleccione un empleado</option>
														<?php foreach ($empleados as $empleado): ?>
															<option value="<?php echo $empleado->getId(); ?>"><?php echo htmlspecialchars($empleado->getNombre()); ?></option>
														<?php endforeach; ?>
													</select>
													<small class="form-text text-muted">Solo se muestran empleados que no están asignados a otros puestos.</small>
												</div>
												<button type="button" class="btn btn-success btn-sm btn-asignar-turno w-100"
												        data-id-puesto="<?php echo $puesto->getId(); ?>"
												        data-descripcion-puesto="<?php echo htmlspecialchars($puesto->getDescripcion()); ?>">
													<i class="fa fa-check-circle me-1"></i> Asignar Turno
												</button>
											<?php else: ?>
												<div class="alert alert-warning">
													No hay empleados disponibles para asignar. Todos los empleados activos ya están asignados a otros puestos o puede <a href="iempleado" class="alert-link">crear un nuevo empleado</a>.
												</div>
											<?php endif; ?>
										<?php endif; ?>
									</div>
								</div>
							</div>
						<?php endforeach; ?>
					<?php endif; ?>
				</div>
			</div>
		</div>
		<?php #endregion TURNOS SECTION ?>
		<!-- Modal para crear nueva cita -->
		<div class="modal fade" id="modal-nueva-cita" tabindex="-1" aria-labelledby="modal-nueva-cita-label" aria-hidden="true">
			<div class="modal-dialog modal-lg">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="modal-nueva-cita-label">Nueva Cita</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<form id="form-nueva-cita">
							<div class="mb-3">
								<label class="form-label">Escoja el turno que atenderá la cita</label>
								<input type="hidden" id="turno-empleado" name="id_empleado_turno" required>
								<div id="turnos-container" class="turnos-container">
									<!-- Los turnos se cargarán dinámicamente mediante AJAX -->
									<div class="text-center py-3">
										<div class="spinner-border text-primary" role="status">
											<span class="visually-hidden">Cargando...</span>
										</div>
									</div>
								</div>
								<div class="invalid-feedback">Por favor seleccione un turno de empleado.</div>
							</div>

							<!-- Employee Services Information Panel -->
							<div class="mb-3" id="employee-services-panel" style="display: none;">
								<div class="alert alert-info border-0" style="background-color: #1e3a5f; border-left: 4px solid #007bff;">
									<div class="d-flex align-items-center">
										<i class="fa fa-info-circle me-2 text-primary"></i>
										<div>
											<h6 class="mb-3 text-white">Servicios que puede realizar este barbero:</h6>
											<div id="employee-services-list" class="text-light">
												<!-- Services will be populated via JavaScript -->
											</div>
										</div>
									</div>
								</div>
							</div>

							<div class="mb-3">
								<label class="form-label fw-bold fs-5">Servicios Disponibles</label>

								<!-- Search field for services -->
								<div class="mb-2">
									<div class="position-relative">
										<input type="text" class="form-control" id="search-servicios" placeholder="Buscar servicios..." style="padding-right: 35px;">
										<button type="button" class="btn btn-sm position-absolute" id="clear-search-servicios"
												style="right: 5px; top: 50%; transform: translateY(-50%); border: none; background: none; color: #6c757d; display: none;">
											<i class="fa fa-times"></i>
										</button>
									</div>
								</div>

								<div id="servicios-container" class="border rounded p-2 bg-light">
									<!-- Los servicios se cargarán dinámicamente mediante AJAX -->
									<div class="text-center py-3">
										<div class="spinner-border text-primary" role="status">
											<span class="visually-hidden">Cargando...</span>
										</div>
									</div>
								</div>
								<div class="invalid-feedback">Por favor seleccione al menos un servicio.</div>
							</div>

							<!-- El total se mostrará dentro del contenedor de servicios -->
							<input type="hidden" id="total-servicios" readonly>
						</form>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
						<button type="button" class="btn btn-primary" id="btn-guardar-cita">Guardar</button>
					</div>
				</div>
			</div>
		</div>

		<!-- Modal para editar cita -->
		<div class="modal fade" id="modal-editar-cita" tabindex="-1" aria-labelledby="modal-editar-cita-label" aria-hidden="true">
			<div class="modal-dialog modal-lg">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="modal-editar-cita-label">Editar Servicios de Cita</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<form id="form-editar-cita">
							<input type="hidden" id="editar-id-cita" name="id_cita" required>

							<!-- Información del empleado y puesto -->
							<div class="mb-3">
								<div class="d-flex justify-content-between align-items-center mb-2">
									<label class="form-label fw-bold fs-5 mb-0">Información de la Cita</label>
									<span class="badge bg-primary" id="editar-info-cita"></span>
								</div>
								<div class="employee-info-container bg-dark text-white p-3 rounded mb-3">
									<div class="d-flex align-items-center">
										<i class="fa fa-user-circle me-2 fs-4"></i>
										<div>
											<div class="fw-bold fs-5" id="editar-nombre-empleado">Cargando...</div>
											<div class="text-muted" id="editar-puesto-empleado">Cargando...</div>
										</div>
									</div>
								</div>
							</div>

							<div class="mb-3">
								<div class="d-flex justify-content-between align-items-center mb-2">
									<label class="form-label fw-bold fs-5 mb-0 mt-2">Servicios Actuales</label>
									<button type="button" class="btn btn-danger" id="btn-eliminar-servicios-seleccionados" disabled>
										<i class="fa fa-trash me-2"></i> Eliminar servicios seleccionados
									</button>
								</div>
								<div id="servicios-actuales-container" class="border rounded turnos-container" style="max-height: 250px; overflow-y: auto; padding: 10px; border-radius: 8px; background-color: #212529; border: 1px solid #495057; margin-bottom: 1rem;">
									<!-- Los servicios actuales se cargarán dinámicamente mediante AJAX -->
									<div class="text-center py-3">
										<div class="spinner-border text-primary" role="status">
											<span class="visually-hidden">Cargando...</span>
										</div>
									</div>
								</div>
							</div>

							<div class="mb-3">
								<label class="form-label fw-bold fs-5 mb-2">Servicios para agregar</label>
								<div id="editar-servicios-container" class="border rounded turnos-container" style="max-height: 250px; overflow-y: auto; padding: 10px; border-radius: 8px; background-color: #212529; border: 1px solid #495057;">
									<!-- Los servicios disponibles se cargarán dinámicamente mediante AJAX -->
									<div class="text-center py-3">
										<div class="spinner-border text-primary" role="status">
											<span class="visually-hidden">Cargando...</span>
										</div>
									</div>
								</div>
								<div class="invalid-feedback">Por favor seleccione al menos un servicio.</div>
							</div>

							<!-- Campos ocultos para los totales -->
							<input type="hidden" id="editar-total-servicios" readonly>
							<input type="hidden" id="editar-total-servicios-actuales" readonly>

							<!-- Total de la cita -->
							<div class="mb-3">
								<div id="total-cita-container" class="border rounded p-3 bg-success text-white" style="border-radius: 8px; background-color: #212529; border: 1px solid #495057;">
									<div class="d-flex justify-content-between align-items-center">
										<span class="fw-bold fs-5">Total de la cita:</span>
										<span id="total-cita-valor" class="total-value text-white fs-4">$0</span>
									</div>
								</div>
							</div>
						</form>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
						<button type="button" class="btn btn-primary" id="btn-guardar-editar-cita">Guardar Cambios</button>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- END #content -->

	<!-- Modal para ver citas programadas -->
	<div class="modal fade" id="modal-citas-programadas" tabindex="-1" aria-labelledby="modal-citas-programadas-label" aria-hidden="true">
		<div class="modal-dialog modal-xl">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="modal-citas-programadas-label">Citas Programadas</h5>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body">
					<!-- Filtros -->
					<div class="row mb-3">
						<div class="col-md-3">
							<label for="filtro-fecha-programada" class="form-label">Fecha</label>
							<div class="input-group">
								<input type="text" class="form-control datepicker" id="filtro-fecha-programada">
								<span class="input-group-text date-icon" id="filtro-fecha-programada-icon">
									<i class="fa fa-calendar"></i>
								</span>
							</div>
						</div>
						<div class="col-md-3">
							<label for="filtro-empleado-programada" class="form-label">Empleado</label>
							<select class="form-select" id="filtro-empleado-programada">
								<option value="">Todos los empleados</option>
							</select>
						</div>
						<div class="col-md-4">
							<label for="filtro-cliente-programada" class="form-label">Cliente (nombre o teléfono)</label>
							<input type="text" class="form-control" id="filtro-cliente-programada" placeholder="Buscar por nombre o teléfono">
						</div>
						<div class="col-md-2 d-flex align-items-end">
							<button type="button" class="btn btn-primary w-100" id="btn-aplicar-filtros-programadas">
								<i class="fa fa-search me-1"></i> Buscar
							</button>
						</div>
					</div>

					<!-- Lista de citas programadas -->
					<div id="lista-citas-programadas">
						<div class="text-center py-4">
							<div class="spinner-border text-primary" role="status">
								<span class="visually-hidden">Cargando...</span>
							</div>
							<p class="mt-2">Cargando citas programadas...</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Modal para cancelar cita programada -->
	<div class="modal fade" id="modal-cancelar-cita-programada" tabindex="-1" aria-labelledby="modal-cancelar-cita-programada-label" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="modal-cancelar-cita-programada-label">Cancelar Cita Programada</h5>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body">
					<form id="form-cancelar-cita-programada">
						<input type="hidden" id="cancelar-id-cita-programada" name="id_cita">

						<div class="mb-3">
							<label for="razon-cancelacion-programada" class="form-label">Razón de Cancelación</label>
							<textarea class="form-control" id="razon-cancelacion-programada" name="razon_cancelacion" rows="3" required></textarea>
							<div class="invalid-feedback">La razón de cancelación es obligatoria.</div>
						</div>
					</form>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
					<button type="button" class="btn btn-danger" id="btn-confirmar-cancelar-cita-programada">Confirmar Cancelación</button>
				</div>
			</div>
		</div>
	</div>

	<!-- Modal para cancelar cita -->
	<div class="modal fade" id="modal-cancelar-cita" tabindex="-1" aria-labelledby="modal-cancelar-cita-label" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header bg-danger text-white">
					<h5 class="modal-title" id="modal-cancelar-cita-label">Cancelar Cita</h5>
					<button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body">
					<form id="form-cancelar-cita">
						<input type="hidden" id="cancelar-id-cita" name="id_cita" required>

						<div class="alert alert-warning">
							<i class="fa fa-exclamation-triangle me-2"></i>
							<strong>Advertencia:</strong> La cancelación de una cita es permanente y no se puede deshacer.
						</div>

						<div class="mb-3">
							<label for="razon-cancelacion" class="form-label">Razón de cancelación <span class="text-danger">*</span></label>
							<textarea class="form-control" id="razon-cancelacion" name="razon_cancelacion" rows="3" required></textarea>
							<div class="invalid-feedback">Por favor ingrese la razón de cancelación.</div>
						</div>
					</form>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
					<button type="button" class="btn btn-danger" id="btn-confirmar-cancelacion">Confirmar Cancelación</button>
				</div>
			</div>
		</div>
	</div>

	<!-- Modal para finalizar cita -->
	<div class="modal fade" id="modal-finalizar-cita" tabindex="-1" aria-labelledby="modal-finalizar-cita-label" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="modal-finalizar-cita-label">Finalizar Cita</h5>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body">
					<form id="form-finalizar-cita">
						<input type="hidden" id="finalizar-id-cita" name="id_cita">

						<!-- Información del empleado -->
						<div class="mb-3">
							<label class="form-label fw-bold">Empleado:</label>
							<p id="finalizar-nombre-empleado" class="mb-0 text-muted">Cargando...</p>
						</div>

						<!-- Tabla de servicios -->
						<div class="mb-3">
							<label class="form-label fw-bold">Servicios de la cita:</label>
							<div class="table-responsive">
								<table class="table table-sm table-bordered">
									<thead class="table-dark">
										<tr>
											<th>Servicio</th>
											<th class="text-end">Precio</th>
										</tr>
									</thead>
									<tbody id="finalizar-servicios-tbody" class="table-dark">
										<tr>
											<td colspan="2" class="text-center">
												<div class="spinner-border spinner-border-sm" role="status">
													<span class="visually-hidden">Cargando...</span>
												</div>
												Cargando servicios...
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<!-- Total -->
						<div class="mb-3">
							<div class="border rounded p-3 bg-success text-white">
								<div class="d-flex justify-content-between align-items-center">
									<span class="fw-bold fs-5">Total de la cita:</span>
									<span id="finalizar-total-valor" class="fs-4">$0</span>
								</div>
							</div>
						</div>

						<!-- Método de pago -->
						<div class="mb-3">
							<label for="finalizar-metodo-pago" class="form-label fw-bold">Método de Pago <span class="text-danger">*</span></label>
							<select class="form-select" id="finalizar-metodo-pago" name="id_metodo_pago" required>
								<option value="">Seleccione un método de pago...</option>
							</select>
							<div class="invalid-feedback">
								Por favor seleccione un método de pago.
							</div>
						</div>
					</form>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
					<button type="button" class="btn btn-success" id="btn-confirmar-finalizacion">
						<i class="fa fa-check-circle me-1"></i> Finalizar Cita
					</button>
				</div>
			</div>
		</div>
	</div>

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- Script para la gestión de turnos -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Inicializar tooltips de Bootstrap
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
        // Botones para asignar turno
        const asignarTurnoBtns = document.querySelectorAll('.btn-asignar-turno');
        asignarTurnoBtns.forEach(btn => {
            btn.addEventListener('click', function () {
                const idPuesto          = this.dataset.idPuesto;
                const descripcionPuesto = this.dataset.descripcionPuesto;
                const selectEmpleado    = document.getElementById('empleado-' + idPuesto);
                const idEmpleado        = selectEmpleado.value;
                const nombreEmpleado    = selectEmpleado.options[selectEmpleado.selectedIndex].text;

                if (!idEmpleado) {
                    swal({
                        title  : 'Error',
                        text   : 'Debe seleccionar un empleado',
                        icon   : 'error',
                        buttons: {
                            confirm: {
                                text      : 'Ok',
                                value     : true,
                                visible   : true,
                                className : 'btn-danger',
                                closeModal: true
                            }
                        }
                    });
                    return;
                }

                // Confirmar asignación
                swal({
                    title  : '¿Asignar turno?',
                    text   : `¿Está seguro de asignar a ${nombreEmpleado} al puesto ${descripcionPuesto}?`,
                    icon   : 'warning',
                    buttons: {
                        cancel : {
                            text      : 'Cancelar',
                            value     : null,
                            visible   : true,
                            className : 'btn-default',
                            closeModal: true
                        },
                        confirm: {
                            text      : 'Sí, asignar',
                            value     : true,
                            visible   : true,
                            className : 'btn-success',
                            closeModal: true
                        }
                    }
                }).then((result) => {
                    if (result) {
                        // Enviar solicitud AJAX para asignar turno
                        const formData = new FormData();
                        formData.append('action', 'asignar_turno');
                        formData.append('id_puesto', idPuesto);
                        formData.append('id_empleado', idEmpleado);

                        fetch('dashboard', {
                            method: 'POST',
                            body  : formData
                        })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    swal({
                                        title  : 'Éxito',
                                        text   : data.message,
                                        icon   : 'success',
                                        buttons: {
                                            confirm: {
                                                text      : 'Ok',
                                                value     : true,
                                                visible   : true,
                                                className : 'btn-success',
                                                closeModal: true
                                            }
                                        }
                                    }).then(() => {
                                        // Recargar la página para mostrar los cambios
                                        window.location.reload();
                                    });
                                } else {
                                    swal({
                                        title  : 'Error',
                                        text   : data.message,
                                        icon   : 'error',
                                        buttons: {
                                            confirm: {
                                                text      : 'Ok',
                                                value     : true,
                                                visible   : true,
                                                className : 'btn-danger',
                                                closeModal: true
                                            }
                                        }
                                    });
                                }
                            })
                            .catch(error => {
                                swal({
                                    title  : 'Error',
                                    text   : 'Ocurrió un error al procesar la solicitud',
                                    icon   : 'error',
                                    buttons: {
                                        confirm: {
                                            text      : 'Ok',
                                            value     : true,
                                            visible   : true,
                                            className : 'btn-danger',
                                            closeModal: true
                                        }
                                    }
                                });
                                console.error('Error:', error);
                            });
                    }
                });
            });
        });

        // Botones para finalizar turno
        const finalizarTurnoBtns = document.querySelectorAll('.btn-finalizar-turno');
        finalizarTurnoBtns.forEach(btn => {
            btn.addEventListener('click', function () {
                const idTurno           = this.dataset.idTurno;
                const idPuesto          = this.dataset.idPuesto;
                const descripcionPuesto = this.dataset.descripcionPuesto;

                // Confirmar finalización
                swal({
                    title  : '¿Finalizar turno?',
                    text   : `¿Está seguro de finalizar el turno actual en el puesto ${descripcionPuesto}?`,
                    icon   : 'warning',
                    buttons: {
                        cancel : {
                            text      : 'Cancelar',
                            value     : null,
                            visible   : true,
                            className : 'btn-default',
                            closeModal: true
                        },
                        confirm: {
                            text      : 'Sí, finalizar',
                            value     : true,
                            visible   : true,
                            className : 'btn-danger',
                            closeModal: true
                        }
                    }
                }).then((result) => {
                    if (result) {
                        // Enviar solicitud AJAX para finalizar turno
                        const formData = new FormData();
                        formData.append('action', 'finalizar_turno');
                        formData.append('id_turno', idTurno);

                        fetch('dashboard', {
                            method: 'POST',
                            body  : formData
                        })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    swal({
                                        title  : 'Éxito',
                                        text   : data.message,
                                        icon   : 'success',
                                        buttons: {
                                            confirm: {
                                                text      : 'Ok',
                                                value     : true,
                                                visible   : true,
                                                className : 'btn-success',
                                                closeModal: true
                                            }
                                        }
                                    }).then(() => {
                                        // Recargar la página para mostrar los cambios
                                        window.location.reload();
                                    });
                                } else {
                                    swal({
                                        title  : 'Error',
                                        text   : data.message,
                                        icon   : 'error',
                                        buttons: {
                                            confirm: {
                                                text      : 'Ok',
                                                value     : true,
                                                visible   : true,
                                                className : 'btn-danger',
                                                closeModal: true
                                            }
                                        }
                                    });
                                }
                            })
                            .catch(error => {
                                swal({
                                    title  : 'Error',
                                    text   : 'Ocurrió un error al procesar la solicitud',
                                    icon   : 'error',
                                    buttons: {
                                        confirm: {
                                            text      : 'Ok',
                                            value     : true,
                                            visible   : true,
                                            className : 'btn-danger',
                                            closeModal: true
                                        }
                                    }
                                });
                                console.error('Error:', error);
                            });
                    }
                });
            });
        });

        // Funcionalidad para finalizar citas
        const finalizarCitaBtns = document.querySelectorAll('.btn-finalizar-cita');
        const modalFinalizarCita = new bootstrap.Modal(document.getElementById('modal-finalizar-cita'));
        const formFinalizarCita = document.getElementById('form-finalizar-cita');
        const finalizarIdCita = document.getElementById('finalizar-id-cita');
        const finalizarNombreEmpleado = document.getElementById('finalizar-nombre-empleado');
        const finalizarServiciosTbody = document.getElementById('finalizar-servicios-tbody');
        const finalizarTotalValor = document.getElementById('finalizar-total-valor');
        const finalizarMetodoPago = document.getElementById('finalizar-metodo-pago');
        const btnConfirmarFinalizacion = document.getElementById('btn-confirmar-finalizacion');

        finalizarCitaBtns.forEach(btn => {
            btn.addEventListener('click', function () {
                const idCita = this.dataset.idCita;

                // Establecer el ID de la cita
                finalizarIdCita.value = idCita;

                // Resetear el formulario
                formFinalizarCita.reset();
                finalizarIdCita.value = idCita; // Mantener el ID después del reset

                // Mostrar estado de carga
                finalizarNombreEmpleado.textContent = 'Cargando...';
                finalizarServiciosTbody.innerHTML = `
                    <tr>
                        <td colspan="2" class="text-center">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">Cargando...</span>
                            </div>
                            Cargando servicios...
                        </td>
                    </tr>
                `;
                finalizarTotalValor.textContent = '$0';

                // Cargar datos de la cita y métodos de pago
                cargarDatosFinalizacion(idCita);
                cargarMetodosPago();

                // Mostrar el modal
                modalFinalizarCita.show();
            });
        });

        // Función para cargar los datos de la cita para finalización
        function cargarDatosFinalizacion(idCita) {
            fetch('obtener-datos-cita', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `id_cita=${idCita}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Mostrar nombre del empleado
                    finalizarNombreEmpleado.textContent = data.empleado || 'No disponible';

                    // Mostrar servicios
                    let serviciosHtml = '';
                    let total = 0;

                    if (data.servicios && data.servicios.length > 0) {
                        data.servicios.forEach(servicio => {
                            const precio = parseFloat(servicio.valor) || 0;
                            total += precio;
                            serviciosHtml += `
                                <tr>
                                    <td>${servicio.descripcion}</td>
                                    <td class="text-end">$${formatCurrency(precio)}</td>
                                </tr>
                            `;
                        });
                    } else {
                        serviciosHtml = `
                            <tr>
                                <td colspan="2" class="text-center text-muted">No hay servicios asociados</td>
                            </tr>
                        `;
                    }

                    finalizarServiciosTbody.innerHTML = serviciosHtml;
                    finalizarTotalValor.textContent = `$${formatCurrency(total)}`;
                } else {
                    finalizarNombreEmpleado.textContent = 'Error al cargar datos';
                    finalizarServiciosTbody.innerHTML = `
                        <tr>
                            <td colspan="2" class="text-center text-danger">Error al cargar servicios</td>
                        </tr>
                    `;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                finalizarNombreEmpleado.textContent = 'Error al cargar datos';
                finalizarServiciosTbody.innerHTML = `
                    <tr>
                        <td colspan="2" class="text-center text-danger">Error al cargar servicios</td>
                    </tr>
                `;
            });
        }

        // Función para cargar métodos de pago
        function cargarMetodosPago() {
            fetch('obtener-metodos-pago', {
                method: 'GET'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    finalizarMetodoPago.innerHTML = '<option value="">Seleccione un método de pago...</option>';
                    data.metodos_pago.forEach(metodo => {
                        finalizarMetodoPago.innerHTML += `<option value="${metodo.id}">${metodo.descripcion}</option>`;
                    });
                } else {
                    finalizarMetodoPago.innerHTML = '<option value="">Error al cargar métodos de pago</option>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                finalizarMetodoPago.innerHTML = '<option value="">Error al cargar métodos de pago</option>';
            });
        }

        // Evento para confirmar la finalización
        btnConfirmarFinalizacion.addEventListener('click', function() {
            // Validar que se haya seleccionado un método de pago
            if (!finalizarMetodoPago.value) {
                finalizarMetodoPago.classList.add('is-invalid');
                return;
            }

            finalizarMetodoPago.classList.remove('is-invalid');

            // Enviar solicitud AJAX para finalizar cita
            const formData = new FormData();
            formData.append('id_cita', finalizarIdCita.value);
            formData.append('id_metodo_pago', finalizarMetodoPago.value);

            fetch('finalizar-cita', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    modalFinalizarCita.hide();
                    swal({
                        title: 'Éxito',
                        text: data.message,
                        icon: 'success',
                        buttons: {
                            confirm: {
                                text: 'Ok',
                                value: true,
                                visible: true,
                                className: 'btn-success',
                                closeModal: true
                            }
                        }
                    }).then(() => {
                        // Recargar la página para mostrar los cambios
                        window.location.reload();
                    });
                } else {
                    swal({
                        title: 'Error',
                        text: data.message,
                        icon: 'error',
                        buttons: {
                            confirm: {
                                text: 'Ok',
                                value: true,
                                visible: true,
                                className: 'btn-danger',
                                closeModal: true
                            }
                        }
                    });
                }
            })
            .catch(error => {
                swal({
                    title: 'Error',
                    text: 'Ocurrió un error al procesar la solicitud',
                    icon: 'error',
                    buttons: {
                        confirm: {
                            text: 'Ok',
                            value: true,
                            visible: true,
                            className: 'btn-danger',
                            closeModal: true
                        }
                    }
                });
                console.error('Error:', error);
            });
        });

        // Funcionalidad para crear nuevas citas
        const btnNuevaCita        = document.getElementById('btn-nueva-cita');
        const modalNuevaCita      = new bootstrap.Modal(document.getElementById('modal-nueva-cita'));
        const formNuevaCita       = document.getElementById('form-nueva-cita');
        const selectTurnoEmpleado = document.getElementById('turno-empleado');
        const serviciosContainer  = document.getElementById('servicios-container');
        const totalServicios      = document.getElementById('total-servicios');
        const btnGuardarCita      = document.getElementById('btn-guardar-cita');

        // Funcionalidad para cancelar citas
        const cancelarCitaBtns            = document.querySelectorAll('.btn-cancelar-cita');
        const modalCancelarCita           = new bootstrap.Modal(document.getElementById('modal-cancelar-cita'));
        const formCancelarCita            = document.getElementById('form-cancelar-cita');
        const cancelarIdCita              = document.getElementById('cancelar-id-cita');
        const razonCancelacion            = document.getElementById('razon-cancelacion');
        const btnConfirmarCancelacion     = document.getElementById('btn-confirmar-cancelacion');

        // Funcionalidad para editar citas
        const modalEditarCita             = new bootstrap.Modal(document.getElementById('modal-editar-cita'));
        const formEditarCita              = document.getElementById('form-editar-cita');
        const editarIdCita                = document.getElementById('editar-id-cita');
        const editarInfoCita              = document.getElementById('editar-info-cita');
        const serviciosActualesContainer  = document.getElementById('servicios-actuales-container');
        const editarServiciosContainer    = document.getElementById('editar-servicios-container');
        const editarTotalServicios        = document.getElementById('editar-total-servicios');
        const btnGuardarEditarCita        = document.getElementById('btn-guardar-editar-cita');
        const btnsEditarCita              = document.querySelectorAll('.btn-editar-cita');

        // Funcionalidad para citas programadas
        const btnVerCitasProgramadas      = document.getElementById('btn-ver-citas-programadas');
        const modalCitasProgramadas       = new bootstrap.Modal(document.getElementById('modal-citas-programadas'));
        const modalCancelarCitaProgramada = new bootstrap.Modal(document.getElementById('modal-cancelar-cita-programada'));
        const filtroFechaProgramada       = document.getElementById('filtro-fecha-programada');
        const filtroEmpleadoProgramada    = document.getElementById('filtro-empleado-programada');
        const filtroClienteProgramada     = document.getElementById('filtro-cliente-programada');
        const btnAplicarFiltrosProgramadas = document.getElementById('btn-aplicar-filtros-programadas');
        const listaCitasProgramadas       = document.getElementById('lista-citas-programadas');
        const btnConfirmarCancelarCitaProgramada = document.getElementById('btn-confirmar-cancelar-cita-programada');

        // Funcionalidad para cerrar caja
        const btnCerrarCaja = document.getElementById('btn-cerrar-caja');


        // Función para formatear valores monetarios en formato colombiano
        function formatCurrency(value) {
            return new Intl.NumberFormat('es-CO', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(value);
        }

        // Función para calcular el total de servicios seleccionados
        function calcularTotalServicios() {
            const serviciosSeleccionados = document.querySelectorAll('input[name="servicios[]"]:checked');
            let total                    = 0;

            serviciosSeleccionados.forEach(servicio => {
                total += parseFloat(servicio.dataset.valor || 0);
            });

            // Guardar el valor en el campo oculto
            totalServicios.value = total;

            // Actualizar o crear el contenedor del total
            let totalContainer = document.querySelector('.total-container');

            // Si no existe el contenedor, crearlo
            if (!totalContainer) {
                totalContainer           = document.createElement('div');
                totalContainer.className = 'total-container';
                serviciosContainer.appendChild(totalContainer);
            }

            // Actualizar el contenido del contenedor
            totalContainer.innerHTML = `
				<span class="total-label">Total:</span>
				<span class="total-value">$${formatCurrency(total)}</span>
			`;

            return total;
        }

        // Función para cargar los turnos de empleados activos
        function cargarTurnosEmpleados() {
            // Obtener el contenedor de turnos
            const turnosContainer = document.getElementById('turnos-container');

            // Mostrar indicador de carga
            turnosContainer.innerHTML = `
				<div class="text-center py-3">
					<div class="spinner-border text-primary" role="status">
						<span class="visually-hidden">Cargando...</span>
					</div>
				</div>
			`;

            // Limpiar el valor del input hidden
            selectTurnoEmpleado.value = '';

            // Realizar petición AJAX
            const formData = new FormData();
            formData.append('action', 'get_turnos_activos');

            fetch('dashboard', {
                method: 'POST',
                body  : formData
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.turnos && data.turnos.length > 0) {
                        // Limpiar el contenedor
                        turnosContainer.innerHTML = '';

                        // Agregar tarjetas de turnos
                        data.turnos.forEach(turno => {
                            // Crear la tarjeta
                            const turnoCard      = document.createElement('div');
                            turnoCard.className  = 'turno-card';
                            turnoCard.dataset.id = turno.id;

                            // Crear el contenido de la tarjeta
                            turnoCard.innerHTML = `
							<div class="empleado-info">
								<span class="empleado-nombre">${turno.nombre_empleado}</span>
								<span class="puesto-descripcion text-muted ms-2">(${turno.descripcion_puesto})</span>
							</div>
						`;

                            // Agregar evento de clic para seleccionar el turno
                            turnoCard.addEventListener('click', function () {
                                // Remover la clase 'selected' de todas las tarjetas
                                document.querySelectorAll('.turno-card').forEach(card => {
                                    card.classList.remove('selected');
                                });

                                // Agregar la clase 'selected' a la tarjeta seleccionada
                                this.classList.add('selected');

                                // Actualizar el valor del input hidden
                                selectTurnoEmpleado.value = this.dataset.id;

                                // Remover la clase 'is-invalid' si estaba presente
                                turnosContainer.classList.remove('is-invalid');

                                // Cargar y mostrar los servicios del empleado
                                cargarServiciosEmpleado(this.dataset.id);
                            });

                            // Agregar la tarjeta al contenedor
                            turnosContainer.appendChild(turnoCard);
                        });
                    } else {
                        // No hay turnos disponibles para citas
                        turnosContainer.innerHTML = `
						<div class="alert alert-warning mb-0">
							No hay turnos disponibles para crear citas. Todos los turnos activos ya tienen citas en progreso o no hay turnos activos.
						</div>
					`;
                    }
                })
                .catch(error => {
                    console.error('Error al cargar turnos:', error);

                    // Mostrar mensaje de error
                    turnosContainer.innerHTML = `
					<div class="alert alert-danger mb-0">
						Error al cargar los turnos. Por favor, intente nuevamente.
					</div>
				`;
                });
        }

        // Función para cargar los servicios disponibles
        function cargarServicios() {
            // Mostrar indicador de carga
            serviciosContainer.innerHTML = `
				<div class="text-center py-3">
					<div class="spinner-border text-primary" role="status">
						<span class="visually-hidden">Cargando...</span>
					</div>
				</div>
			`;

            // Realizar petición AJAX
            const formData = new FormData();
            formData.append('action', 'get_servicios_activos');

            fetch('dashboard', {
                method: 'POST',
                body  : formData
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.servicios && data.servicios.length > 0) {
                        // Limpiar el contenedor
                        serviciosContainer.innerHTML = '';

                        // Agregar servicios al contenedor
                        console.log(`Cargando ${data.servicios.length} servicios...`);
                        data.servicios.forEach((servicio, index) => {
                            const servicioDiv     = document.createElement('div');
                            servicioDiv.className = 'form-check w-100 d-flex align-items-center service-item-hover';

                            // Create the input element first
                            const input         = document.createElement('input');
                            input.className     = 'form-check-input';
                            input.type          = 'checkbox';
                            input.name          = 'servicios[]';
                            input.id            = `servicio-${servicio.id}`;
                            input.value         = servicio.id;
                            input.dataset.valor = servicio.valor;
                            input.style.transform = 'scale(1.2)';
                            input.style.marginLeft = '0';
                            input.style.marginRight = '10px';
                            input.style.marginTop = '0';
                            input.style.cursor = 'pointer';
                            input.addEventListener('change', calcularTotalServicios);

                            // Create the label element
                            const label     = document.createElement('label');
                            label.className = 'form-check-label text-white d-flex justify-content-between align-items-center w-100';
                            label.htmlFor   = `servicio-${servicio.id}`;
                            label.style.margin = '0';
                            label.style.padding = '0';
                            label.innerHTML = `<span class="service-description">${servicio.descripcion}</span> <span class="service-price">$${formatCurrency(servicio.valor)}</span>`;

                            console.log(`Servicio ${index + 1}: ${servicio.descripcion}`);

                            // Add click functionality to the div
                            servicioDiv.addEventListener('click', function(e) {
                                // Skip if clicking on the checkbox itself
                                if (e.target !== input) {
                                    // Toggle the checkbox
                                    input.checked = !input.checked;

                                    // Trigger the change event manually
                                    const changeEvent = new Event('change', { bubbles: true });
                                    input.dispatchEvent(changeEvent);
                                }
                            });

                            // Add click functionality to the label
                            label.addEventListener('click', function(e) {
                                // Prevent the default behavior (which would be to check the checkbox)
                                e.preventDefault();

                                // Toggle the checkbox
                                input.checked = !input.checked;

                                // Trigger the change event manually
                                const changeEvent = new Event('change', { bubbles: true });
                                input.dispatchEvent(changeEvent);

                                // Stop propagation to prevent the div's click handler from firing
                                e.stopPropagation();
                            });

                            servicioDiv.appendChild(input);
                            servicioDiv.appendChild(label);
                            serviciosContainer.appendChild(servicioDiv);
                        });
                    } else {
                        // No hay servicios activos
                        serviciosContainer.innerHTML = `
						<div class="alert alert-warning mb-0">
							No hay servicios activos disponibles.
						</div>
					`;
                    }

                    // Agregar el contenedor para el total
                    const totalContainer     = document.createElement('div');
                    totalContainer.className = 'total-container';
                    totalContainer.innerHTML = `
					<span class="total-label">Total:</span>
					<span class="total-value">$0</span>
				`;
                    serviciosContainer.appendChild(totalContainer);

                    // Inicializar el total
                    calcularTotalServicios();

                    // Prevenir la propagación del evento click en los checkboxes
                    // para evitar que el evento click del div padre se active
                    document.querySelectorAll('input[name="servicios[]"]').forEach(checkbox => {
                        checkbox.addEventListener('click', function(e) {
                            // Importante: detener la propagación para evitar que el div padre
                            // reciba el evento y vuelva a cambiar el estado del checkbox
                            e.stopPropagation();
                        });
                    });

                    // También prevenir la propagación en los spans dentro de los labels
                    document.querySelectorAll('.service-description, .service-price').forEach(span => {
                        span.addEventListener('click', function(e) {
                            // No detener la propagación aquí para permitir que el div padre reciba el evento
                            // y cambie el estado del checkbox
                        });
                    });

                    // Configurar event listeners de búsqueda después de cargar servicios
                    configurarEventListenersBusqueda();

                    // Ejecutar test de funcionalidad después de un breve delay
                    setTimeout(() => {
                        testSearchFunctionality();
                    }, 100);

                    console.log('Servicios cargados exitosamente. Event listeners de búsqueda configurados.');
                })
                .catch(error => {
                    console.error('Error al cargar servicios:', error);

                    // Mostrar mensaje de error
                    serviciosContainer.innerHTML = `
					<div class="alert alert-danger mb-0">
						Error al cargar los servicios. Por favor, intente nuevamente.
					</div>
				`;
                });
        }

        // Función para filtrar servicios basado en el texto de búsqueda
        function filtrarServicios() {
            const searchInput = document.getElementById('search-servicios');
            const clearButton = document.getElementById('clear-search-servicios');

            if (!searchInput || !clearButton) {
                console.warn('Elementos de búsqueda no encontrados');
                return;
            }

            const searchText = searchInput.value.toLowerCase().trim();
            const serviceItems = document.querySelectorAll('#servicios-container .form-check');

            console.log(`Filtrando servicios con texto: "${searchText}", encontrados ${serviceItems.length} elementos`);

            // Mostrar/ocultar botón de limpiar
            if (searchText.length > 0) {
                clearButton.style.display = 'block';
            } else {
                clearButton.style.display = 'none';
            }

            // Si no hay texto de búsqueda, mostrar todos los servicios
            if (searchText.length === 0) {
                serviceItems.forEach(item => {
                    item.style.display = 'block';
                });
                return;
            }

            // Filtrar servicios basado en la descripción (sin mínimo de caracteres)
            let visibleCount = 0;
            serviceItems.forEach(item => {
                const serviceDescription = item.querySelector('.service-description');
                if (serviceDescription) {
                    const description = serviceDescription.textContent.toLowerCase();
                    if (description.includes(searchText)) {
                        item.style.display = 'block';
                        visibleCount++;
                    } else {
                        item.style.display = 'none';
                    }
                } else {
                    // Si no tiene descripción, ocultarlo por seguridad
                    item.style.display = 'none';
                    console.warn('Servicio sin descripción encontrado:', item);
                }
            });

            console.log(`Servicios visibles después del filtro: ${visibleCount}`);
        }

        // Función para limpiar el filtro de búsqueda
        function limpiarFiltroServicios() {
            const searchInput = document.getElementById('search-servicios');
            const clearButton = document.getElementById('clear-search-servicios');

            if (!searchInput || !clearButton) {
                console.warn('Elementos de búsqueda no encontrados para limpiar');
                return;
            }

            searchInput.value = '';
            clearButton.style.display = 'none';

            // Mostrar todos los servicios
            const serviceItems = document.querySelectorAll('#servicios-container .form-check');
            serviceItems.forEach(item => {
                item.style.display = 'block';
            });

            console.log('Filtro de búsqueda limpiado, mostrando todos los servicios');
        }

        // Función para cargar y mostrar los servicios de un empleado
        function cargarServiciosEmpleado(idEmpleadoTurno) {
            const employeeServicesPanel = document.getElementById('employee-services-panel');
            const employeeServicesList = document.getElementById('employee-services-list');

            // Ocultar el panel inicialmente
            employeeServicesPanel.style.display = 'none';

            // Mostrar indicador de carga
            employeeServicesList.innerHTML = `
                <div class="text-center py-2">
                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                        <span class="visually-hidden">Cargando...</span>
                    </div>
                    <span class="ms-2">Cargando servicios del empleado...</span>
                </div>
            `;

            // Mostrar el panel
            employeeServicesPanel.style.display = 'block';

            // Realizar petición AJAX
            const formData = new FormData();
            formData.append('action', 'get_empleado_servicios');
            formData.append('id_empleado_turno', idEmpleadoTurno);

            fetch('dashboard', {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.servicios && data.servicios.length > 0) {
                        // Mostrar los servicios del empleado
                        let serviciosTexto = '';
                        data.servicios.forEach((servicio, index) => {
                            if (index > 0) serviciosTexto += ', ';
                            serviciosTexto += servicio.descripcion;
                        });

                        employeeServicesList.innerHTML = serviciosTexto;
                    } else {
                        // El empleado no tiene servicios asociados
                        employeeServicesList.innerHTML = `
                            <span class="text-warning">
                                <i class="fa fa-exclamation-triangle me-1"></i>
                                Este empleado no tiene servicios asociados
                            </span>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error al cargar servicios del empleado:', error);
                    employeeServicesList.innerHTML = `
                        <span class="text-danger">
                            <i class="fa fa-exclamation-triangle me-1"></i>
                            Error al cargar los servicios del empleado
                        </span>
                    `;
                });
        }

        // Función para validar el formulario
        function validarFormulario() {
            let isValid = true;

            // Validar turno de empleado
            if (!selectTurnoEmpleado.value) {
                document.getElementById('turnos-container').classList.add('is-invalid');
                isValid = false;
            } else {
                document.getElementById('turnos-container').classList.remove('is-invalid');
            }

            // Validar que se haya seleccionado al menos un servicio
            const serviciosSeleccionados = document.querySelectorAll('input[name="servicios[]"]:checked');
            if (serviciosSeleccionados.length === 0) {
                serviciosContainer.classList.add('is-invalid');
                isValid = false;
            } else {
                serviciosContainer.classList.remove('is-invalid');
            }

            return isValid;
        }

        // Evento para abrir el modal y cargar datos
        btnNuevaCita.addEventListener('click', function () {
            // Resetear el formulario
            formNuevaCita.reset();

            // Limpiar cualquier indicador de carga previo
            ocultarCargandoServicios();

            // Ocultar el panel de servicios del empleado
            document.getElementById('employee-services-panel').style.display = 'none';

            // Cargar datos necesarios
            cargarTurnosEmpleados();
            cargarServicios();

            // Mostrar el modal
            modalNuevaCita.show();
        });

        // Limpiar indicadores de carga cuando se cierre el modal
        document.getElementById('modal-nueva-cita').addEventListener('hidden.bs.modal', function () {
            ocultarCargandoServicios();
            // Limpiar referencia a cita programada
            modalNuevaCita._citaProgramadaId = null;
            // Limpiar filtro de búsqueda de servicios
            limpiarFiltroServicios();
        });

        // Función para configurar event listeners de búsqueda
        function configurarEventListenersBusqueda() {
            const searchInput = document.getElementById('search-servicios');
            const clearButton = document.getElementById('clear-search-servicios');

            if (searchInput && clearButton) {
                // Remover listeners existentes para evitar duplicados
                searchInput.removeEventListener('keyup', filtrarServicios);
                searchInput.removeEventListener('input', filtrarServicios);
                clearButton.removeEventListener('click', limpiarFiltroServicios);

                // Agregar listeners nuevamente
                searchInput.addEventListener('keyup', filtrarServicios);
                searchInput.addEventListener('input', filtrarServicios);
                clearButton.addEventListener('click', limpiarFiltroServicios);

                console.log('Event listeners de búsqueda configurados correctamente');
            } else {
                console.warn('No se pudieron encontrar los elementos de búsqueda:', {
                    searchInput: !!searchInput,
                    clearButton: !!clearButton
                });
            }
        }

        // Configurar event listeners iniciales para la búsqueda
        configurarEventListenersBusqueda();

        // Función de prueba para verificar que la búsqueda funciona
        function testSearchFunctionality() {
            const searchInput = document.getElementById('search-servicios');
            const serviceItems = document.querySelectorAll('#servicios-container .form-check');

            console.log('=== TEST BÚSQUEDA ===');
            console.log('Search input encontrado:', !!searchInput);
            console.log('Servicios encontrados:', serviceItems.length);

            serviceItems.forEach((item, index) => {
                const description = item.querySelector('.service-description');
                console.log(`Servicio ${index + 1}:`, description ? description.textContent : 'SIN DESCRIPCIÓN');
            });
            console.log('=== FIN TEST ===');
        }

        // Exponer función de test globalmente para debugging
        window.testSearchFunctionality = testSearchFunctionality;

        // Funciones para citas programadas
        function cargarEmpleadosParaFiltro() {
            const formData = new FormData();
            formData.append('action', 'get_empleados_activos');

            fetch('dashboard', {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.empleados) {
                        filtroEmpleadoProgramada.innerHTML = '<option value="">Todos los empleados</option>';
                        data.empleados.forEach(empleado => {
                            const option = document.createElement('option');
                            option.value = empleado.id;
                            option.textContent = empleado.nombre;
                            filtroEmpleadoProgramada.appendChild(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error al cargar empleados:', error);
                });
        }

        function cargarCitasProgramadas() {
            listaCitasProgramadas.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Cargando...</span>
                    </div>
                    <p class="mt-2">Cargando citas programadas...</p>
                </div>
            `;

            const formData = new FormData();
            formData.append('action', 'get_citas_programadas');

            if (filtroFechaProgramada.value) {
                formData.append('fecha', filtroFechaProgramada.value);
            }
            if (filtroEmpleadoProgramada.value) {
                formData.append('id_empleado', filtroEmpleadoProgramada.value);
            }
            if (filtroClienteProgramada.value.trim()) {
                formData.append('filtro_cliente', filtroClienteProgramada.value.trim());
            }

            fetch('dashboard', {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.citas) {
                        if (data.citas.length > 0) {
                            renderCitasProgramadas(data.citas);
                        } else {
                            listaCitasProgramadas.innerHTML = `
                                <div class="alert alert-info">
                                    <i class="fa fa-info-circle me-2"></i>
                                    No se encontraron citas programadas con los filtros aplicados.
                                </div>
                            `;
                        }
                    } else {
                        listaCitasProgramadas.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="fa fa-exclamation-triangle me-2"></i>
                                Error al cargar las citas programadas: ${data.message || 'Error desconocido'}
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    listaCitasProgramadas.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fa fa-exclamation-triangle me-2"></i>
                            Error al cargar las citas programadas. Por favor, intente nuevamente.
                        </div>
                    `;
                });
        }

        function renderCitasProgramadas(citas) {
            let html = `
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th class="text-center">Acciones</th>
                                <th>Fecha y Hora</th>
                                <th>Cliente</th>
                                <th>Empleado</th>
                                <th>Servicios</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            citas.forEach(cita => {
                const fechaInicio = new Date(cita.fecha_inicio);
                // Format date as yyyy-MM-dd
                const fechaFormateada = fechaInicio.toISOString().split('T')[0];
                const horaFormateada = fechaInicio.toLocaleTimeString('es-CO', { hour: '2-digit', minute: '2-digit' });

                html += `
                    <tr>
                        <td class="text-center">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-success btn-crear-cita-desde-programada"
                                        data-cita='${JSON.stringify(cita)}' title="Crear Cita">
                                    <i class="fa fa-calendar-plus"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-danger btn-cancelar-cita-programada"
                                        data-id-cita="${cita.id}" title="Cancelar">
                                    <i class="fa fa-times"></i>
                                </button>
                            </div>
                        </td>
                        <td>
                            <strong>${fechaFormateada}</strong><br>
                            <small class="text-muted">${horaFormateada}</small>
                        </td>
                        <td>
                            <strong>${cita.nombre_cliente || 'Sin cliente'}</strong><br>
                            <small class="text-muted">${cita.celular_cliente || ''}</small>
                        </td>
                        <td>${cita.nombre_empleado || 'Sin asignar'}</td>
                        <td>
                            <small class="text-muted">
                                ${cita.servicios ? cita.servicios.map(s => s.descripcion).join(', ') : 'Sin servicios'}
                            </small>
                        </td>
                        <td>
                            <strong>$${formatCurrency(cita.total || 0)}</strong>
                        </td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;

            listaCitasProgramadas.innerHTML = html;

            // Agregar event listeners a los botones
            document.querySelectorAll('.btn-crear-cita-desde-programada').forEach(btn => {
                btn.addEventListener('click', function() {
                    const cita = JSON.parse(this.dataset.cita);
                    crearCitaDesdeProgramada(cita);
                });
            });

            document.querySelectorAll('.btn-cancelar-cita-programada').forEach(btn => {
                btn.addEventListener('click', function() {
                    const idCita = this.dataset.idCita;
                    cancelarCitaProgramada(idCita);
                });
            });
        }

        function crearCitaDesdeProgramada(citaProgramada) {
            // Cerrar el modal de citas programadas
            modalCitasProgramadas.hide();

            // Resetear el formulario de nueva cita
            formNuevaCita.reset();

            // Mostrar el modal de nueva cita inmediatamente
            modalNuevaCita.show();

            // Guardar referencia a la cita programada para marcarla como realizada después
            modalNuevaCita._citaProgramadaId = citaProgramada.id;

            // Mostrar indicador de carga inmediatamente después de que el modal se muestre
            setTimeout(() => {
                mostrarCargandoServicios();
            }, 100);

            // Fallback: Asegurar que el overlay esté visible después de un tiempo
            setTimeout(() => {
                const overlay = document.getElementById('servicios-loading-overlay');
                if (!overlay) {
                    console.log('Overlay no encontrado, creando fallback');
                    mostrarCargandoServicios();
                }
            }, 500);

            // Cargar datos necesarios
            cargarTurnosEmpleados();
            cargarServicios();

            // Pre-poblar los datos cuando el modal esté completamente cargado
            setTimeout(() => {
                // Si hay empleado asignado, intentar seleccionarlo
                if (citaProgramada.id_empleado) {
                    // Buscar el turno correspondiente al empleado
                    const turnoCards = document.querySelectorAll('.turno-card');
                    turnoCards.forEach(card => {
                        // Aquí necesitaríamos una forma de identificar el turno por empleado
                        // Por ahora, simplemente mostraremos un mensaje
                    });
                }

                // Pre-seleccionar servicios si están disponibles
                if (citaProgramada.servicios && citaProgramada.servicios.length > 0) {
                    // Esperar un poco más para asegurar que los servicios estén cargados
                    setTimeout(() => {
                        let serviciosSeleccionados = 0;
                        citaProgramada.servicios.forEach(servicio => {
                            const checkbox = document.querySelector(`input[value="${servicio.id}"]`);
                            if (checkbox) {
                                checkbox.checked = true;
                                checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                                serviciosSeleccionados++;
                            }
                        });

                        console.log(`Servicios pre-seleccionados: ${serviciosSeleccionados} de ${citaProgramada.servicios.length}`);

                        // Ocultar indicador de carga después de pre-seleccionar servicios
                        setTimeout(() => {
                            ocultarCargandoServicios();
                        }, 300);
                    }, 1200);
                } else {
                    // Si no hay servicios, ocultar el indicador después de un tiempo
                    setTimeout(() => {
                        ocultarCargandoServicios();
                    }, 1200);
                }
            }, 1500);
        }

        function mostrarCargandoServicios() {
            // Primero, remover cualquier overlay existente
            ocultarCargandoServicios();

            let serviciosContainer = document.getElementById('servicios-container');

            // Si no se encuentra el contenedor de servicios, intentar con el modal body
            if (!serviciosContainer) {
                console.log('Contenedor de servicios no encontrado, usando modal body');
                serviciosContainer = document.querySelector('#modal-nueva-cita .modal-body');
            }

            if (serviciosContainer) {
                console.log('Mostrando indicador de carga en:', serviciosContainer.id || 'modal body');

                // Crear overlay de carga
                const loadingOverlay = document.createElement('div');
                loadingOverlay.id = 'servicios-loading-overlay';
                loadingOverlay.className = 'position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center';
                loadingOverlay.style.cssText = `
                    background-color: rgba(33, 37, 41, 0.95);
                    z-index: 9999;
                    border-radius: 0.375rem;
                    min-height: 200px;
                `;

                loadingOverlay.innerHTML = `
                    <div class="text-center text-white">
                        <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                            <span class="visually-hidden">Cargando...</span>
                        </div>
                        <div class="fw-bold">Preparando servicios de la cita programada...</div>
                        <div class="small text-muted mt-1">Por favor espere...</div>
                    </div>
                `;

                // Asegurar que el contenedor tenga posición relativa y altura mínima
                serviciosContainer.style.position = 'relative';
                serviciosContainer.style.minHeight = '200px';
                serviciosContainer.appendChild(loadingOverlay);

                console.log('Overlay de carga agregado al DOM');

                // Verificar que el overlay sea visible
                setTimeout(() => {
                    const overlay = document.getElementById('servicios-loading-overlay');
                    if (overlay) {
                        console.log('Overlay confirmado en DOM:', overlay.offsetHeight > 0 ? 'visible' : 'no visible');
                    }
                }, 200);
            } else {
                console.error('No se encontró ningún contenedor válido para el overlay');

                // Último recurso: mostrar alerta temporal
                const alertDiv = document.createElement('div');
                alertDiv.id = 'servicios-loading-overlay';
                alertDiv.className = 'alert alert-info text-center';
                alertDiv.innerHTML = `
                    <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                    Preparando servicios de la cita programada...
                `;

                const modalBody = document.querySelector('#modal-nueva-cita .modal-body');
                if (modalBody) {
                    modalBody.insertBefore(alertDiv, modalBody.firstChild);
                }
            }
        }

        function ocultarCargandoServicios() {
            const loadingOverlay = document.getElementById('servicios-loading-overlay');
            if (loadingOverlay) {
                console.log('Ocultando indicador de carga de servicios');
                loadingOverlay.remove();
            }
        }

        function cancelarCitaProgramada(idCita) {
            // Open modal to get cancellation reason
            document.getElementById('cancelar-id-cita-programada').value = idCita;
            document.getElementById('razon-cancelacion-programada').value = '';
            document.getElementById('razon-cancelacion-programada').classList.remove('is-invalid');

            modalCancelarCitaProgramada.show();
        }

        function confirmarCancelacionCitaProgramada() {
            // Get form data
            const idCita = document.getElementById('cancelar-id-cita-programada').value;
            const razonCancelacion = document.getElementById('razon-cancelacion-programada').value.trim();

            // Validate cancellation reason
            if (!razonCancelacion) {
                document.getElementById('razon-cancelacion-programada').classList.add('is-invalid');
                return;
            }

            // Prepare data to send
            const formData = new FormData();
            formData.append('action', 'cancelar_cita_programada');
            formData.append('id_cita', idCita);
            formData.append('razon_cancelacion', razonCancelacion);

            // Disable button and show loading indicator
            btnConfirmarCancelarCitaProgramada.disabled = true;
            btnConfirmarCancelarCitaProgramada.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Cancelando...';

            // Send request
            fetch('dashboard', {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    // Restore button
                    btnConfirmarCancelarCitaProgramada.disabled = false;
                    btnConfirmarCancelarCitaProgramada.innerHTML = 'Confirmar Cancelación';

                    if (data.success) {
                        // Close modal
                        modalCancelarCitaProgramada.hide();

                        // Show success message
                        swal({
                            title: 'Éxito',
                            text: 'Cita programada cancelada correctamente',
                            icon: 'success',
                            buttons: {
                                confirm: {
                                    text: 'Ok',
                                    value: true,
                                    visible: true,
                                    className: 'btn-success',
                                    closeModal: true
                                }
                            }
                        }).then(() => {
                            // Reload scheduled appointments
                            cargarCitasProgramadas();
                        });
                    } else {
                        swal({
                            title: 'Error',
                            text: data.message,
                            icon: 'error',
                            buttons: {
                                confirm: {
                                    text: 'Ok',
                                    value: true,
                                    visible: true,
                                    className: 'btn-danger',
                                    closeModal: true
                                }
                            }
                        });
                    }
                })
                .catch(error => {
                    // Restore button
                    btnConfirmarCancelarCitaProgramada.disabled = false;
                    btnConfirmarCancelarCitaProgramada.innerHTML = 'Confirmar Cancelación';

                    console.error('Error al cancelar cita:', error);
                    swal({
                        title: 'Error',
                        text: 'Error al cancelar la cita programada',
                        icon: 'error',
                        buttons: {
                            confirm: {
                                text: 'Ok',
                                value: true,
                                visible: true,
                                className: 'btn-danger',
                                closeModal: true
                            }
                        }
                    });
                });
        }

        // Event listeners para citas programadas
        btnVerCitasProgramadas.addEventListener('click', function() {
            // Establecer fecha actual por defecto
            const today = new Date().toISOString().split('T')[0];
            filtroFechaProgramada.value = today;

            // Cargar empleados para el filtro
            cargarEmpleadosParaFiltro();

            // Cargar citas programadas
            cargarCitasProgramadas();

            // Mostrar el modal
            modalCitasProgramadas.show();
        });

        btnAplicarFiltrosProgramadas.addEventListener('click', function() {
            cargarCitasProgramadas();
        });

        btnConfirmarCancelarCitaProgramada.addEventListener('click', function() {
            confirmarCancelacionCitaProgramada();
        });

        // Initialize datepicker for scheduled appointments filter
        $(document).ready(function() {
            // Initialize datepicker
            $('#filtro-fecha-programada').datepicker({
                autoclose: true,
                todayHighlight: true,
                format: 'yyyy-mm-dd'
            });

            // Make calendar icon clickable
            document.getElementById('filtro-fecha-programada-icon').addEventListener('click', function() {
                $('#filtro-fecha-programada').datepicker('show');
            });
        });

        // Función para cargar los servicios de una cita específica
        function cargarServiciosCita(idCita) {
            // Mostrar indicador de carga
            serviciosActualesContainer.innerHTML = `
                <div class="text-center py-3">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Cargando...</span>
                    </div>
                </div>
            `;

            // Realizar petición AJAX
            const formData = new FormData();
            formData.append('action', 'get_servicios_cita');
            formData.append('id_cita', idCita);

            fetch('dashboard', {
                method: 'POST',
                body  : formData
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Actualizar la información de la cita
                        editarInfoCita.textContent = `Cita #${idCita} - ${data.fecha_inicio}`;

                        // Actualizar la información del empleado y puesto
                        document.getElementById('editar-nombre-empleado').textContent = data.nombre_empleado;
                        document.getElementById('editar-puesto-empleado').textContent = data.descripcion_puesto;

                        // Limpiar el contenedor
                        serviciosActualesContainer.innerHTML = '';

                        if (data.servicios && data.servicios.length > 0) {

                            // Agregar servicios al contenedor
                            data.servicios.forEach(servicio => {
                                const servicioDiv = document.createElement('div');
                                servicioDiv.className = 'form-check w-100 d-flex align-items-center service-item-hover';

                                // Create the input element first
                                const input = document.createElement('input');
                                input.className = 'form-check-input servicio-checkbox';
                                input.type = 'checkbox';
                                input.id = `servicio-check-${servicio.id}`;
                                input.dataset.idServicio = servicio.id;
                                input.dataset.idCita = idCita;
                                input.style.transform = 'scale(1.2)';
                                input.style.marginLeft = '0';
                                input.style.marginRight = '10px';
                                input.style.marginTop = '0';
                                input.style.cursor = 'pointer';

                                // Make the entire div clickable
                                servicioDiv.style.cursor = 'pointer';

                                // Add click functionality to the div
                                servicioDiv.addEventListener('click', function(e) {
                                    // Skip if clicking on the checkbox itself
                                    if (e.target !== input) {
                                        // Toggle the checkbox
                                        input.checked = !input.checked;

                                        // Trigger the change event manually
                                        const changeEvent = new Event('change', { bubbles: true });
                                        input.dispatchEvent(changeEvent);
                                    }
                                });

                                // Create the label
                                const label = document.createElement('label');
                                label.className = 'form-check-label text-white d-flex justify-content-between align-items-center w-100';
                                label.htmlFor = `servicio-check-${servicio.id}`;
                                label.style.margin = '0';
                                label.style.padding = '0';
                                label.innerHTML = `<span class="service-description">${servicio.descripcion}</span> <span class="service-price">$${formatCurrency(servicio.valor)}</span>`;

                                // Add click functionality to the spans inside the label
                                // This ensures that clicking on the text also toggles the checkbox
                                label.addEventListener('click', function(e) {
                                    // Prevent the default behavior (which would be to check the checkbox)
                                    e.preventDefault();

                                    // Toggle the checkbox
                                    input.checked = !input.checked;

                                    // Trigger the change event manually
                                    const changeEvent = new Event('change', { bubbles: true });
                                    input.dispatchEvent(changeEvent);

                                    // Stop propagation to prevent the div's click handler from firing
                                    e.stopPropagation();
                                });

                                servicioDiv.appendChild(input);
                                servicioDiv.appendChild(label);
                                serviciosActualesContainer.appendChild(servicioDiv);
                            });

                            // Agregar el botón para eliminar servicios seleccionados al final
                            const botonesContainer = document.createElement('div');
                            botonesContainer.className = 'botones-servicios-container';

                            // Botón para eliminar seleccionados (se creará pero no se usará aquí, ya que se muestra en el encabezado)
                            const btnEliminarSeleccionados = document.createElement('button');
                            btnEliminarSeleccionados.type = 'button';
                            btnEliminarSeleccionados.className = 'btn btn-danger';
                            btnEliminarSeleccionados.id = 'btn-eliminar-servicios-seleccionados';
                            btnEliminarSeleccionados.innerHTML = '<i class="fa fa-trash me-2"></i> Eliminar servicios seleccionados';
                            btnEliminarSeleccionados.disabled = true; // Inicialmente deshabilitado
                            btnEliminarSeleccionados.style.display = 'none'; // Ocultar este botón ya que se muestra en el encabezado

                            // Agregar el total
                            const totalDiv = document.createElement('div');
                            totalDiv.className = 'total-container';
                            totalDiv.style.borderTop = '1px solid rgba(255, 255, 255, 0.2)';
                            totalDiv.innerHTML = `
                                <span class="total-label text-white">Total:</span>
                                <span class="total-value">$${formatCurrency(data.total)}</span>
                            `;
                            serviciosActualesContainer.appendChild(totalDiv);

                            // Guardar el total de servicios actuales en el campo oculto
                            document.getElementById('editar-total-servicios-actuales').value = data.total;

                            // Actualizar el total de la cita
                            actualizarTotalCita();

                            // Agregar evento al botón de eliminar servicios seleccionados
                            document.getElementById('btn-eliminar-servicios-seleccionados').addEventListener('click', function() {
                                eliminarServiciosSeleccionados(idCita);
                            });

                            // Agregar eventos a los checkboxes de servicios
                            document.querySelectorAll('.servicio-checkbox').forEach(checkbox => {
                                checkbox.addEventListener('change', function() {
                                    // Verificar si hay al menos un checkbox seleccionado
                                    const haySeleccionados = Array.from(document.querySelectorAll('.servicio-checkbox')).some(cb => cb.checked);

                                    // Habilitar o deshabilitar el botón de eliminar seleccionados
                                    document.getElementById('btn-eliminar-servicios-seleccionados').disabled = !haySeleccionados;
                                });
                            });

                            // Prevenir la propagación del evento click en los checkboxes
                            // para evitar que el evento click del div padre se active
                            document.querySelectorAll('.servicio-checkbox').forEach(checkbox => {
                                checkbox.addEventListener('click', function(e) {
                                    e.stopPropagation();
                                });
                            });
                        } else {
                            // No hay servicios
                            serviciosActualesContainer.innerHTML = `
                                <div class="alert alert-warning mb-0">
                                    No hay servicios asociados a esta cita.
                                </div>
                            `;
                        }
                    } else {
                        // Error al cargar los servicios
                        serviciosActualesContainer.innerHTML = `
                            <div class="alert alert-danger mb-0">
                                Error al cargar los servicios: ${data.message}
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    serviciosActualesContainer.innerHTML = `
                        <div class="alert alert-danger mb-0">
                            Error al cargar los servicios. Por favor, intente nuevamente.
                        </div>
                    `;
                });
        }

        // Función para cargar los servicios disponibles para agregar a una cita
        function cargarServiciosDisponibles(idCita) {
            // Mostrar indicador de carga
            editarServiciosContainer.innerHTML = `
                <div class="text-center py-3">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Cargando...</span>
                    </div>
                </div>
            `;

            // Realizar petición AJAX
            const formData = new FormData();
            formData.append('action', 'get_servicios_disponibles');
            formData.append('id_cita', idCita);

            fetch('dashboard', {
                method: 'POST',
                body  : formData
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.servicios && data.servicios.length > 0) {
                        // Limpiar el contenedor
                        editarServiciosContainer.innerHTML = '';

                        // Agregar servicios al contenedor
                        data.servicios.forEach(servicio => {
                            const servicioDiv     = document.createElement('div');
                            servicioDiv.className = 'form-check w-100 d-flex align-items-center service-item-hover';

                            // Create the input element first
                            const input = document.createElement('input');
                            input.className     = 'form-check-input';
                            input.type          = 'checkbox';
                            input.name          = 'editar_servicios[]';
                            input.id            = `editar-servicio-${servicio.id}`;
                            input.value         = servicio.id;
                            input.dataset.valor = servicio.valor;
                            input.style.transform = 'scale(1.2)';
                            input.style.marginLeft = '0';
                            input.style.marginRight = '10px';
                            input.style.marginTop = '0';
                            input.style.cursor = 'pointer';
                            input.addEventListener('change', calcularTotalEditarServicios);

                            // Make the entire div clickable
                            servicioDiv.style.cursor = 'pointer';

                            // Add click functionality to the div
                            servicioDiv.addEventListener('click', function(e) {
                                // Skip if clicking on the checkbox itself
                                if (e.target !== input) {
                                    // Toggle the checkbox
                                    input.checked = !input.checked;

                                    // Trigger the change event manually
                                    const changeEvent = new Event('change', { bubbles: true });
                                    input.dispatchEvent(changeEvent);
                                }
                            });

                            // Create the label
                            const label = document.createElement('label');
                            label.className = 'form-check-label text-white d-flex justify-content-between align-items-center w-100';
                            label.htmlFor   = `editar-servicio-${servicio.id}`;
                            label.style.margin = '0';
                            label.style.padding = '0';
                            label.innerHTML = `<span class="service-description">${servicio.descripcion}</span> <span class="service-price">$${formatCurrency(servicio.valor)}</span>`;

                            // Add click functionality to the spans inside the label
                            // This ensures that clicking on the text also toggles the checkbox
                            label.addEventListener('click', function(e) {
                                // Prevent the default behavior (which would be to check the checkbox)
                                e.preventDefault();

                                // Toggle the checkbox
                                input.checked = !input.checked;

                                // Trigger the change event manually
                                const changeEvent = new Event('change', { bubbles: true });
                                input.dispatchEvent(changeEvent);

                                // Stop propagation to prevent the div's click handler from firing
                                e.stopPropagation();
                            });

                            servicioDiv.appendChild(input);
                            servicioDiv.appendChild(label);
                            editarServiciosContainer.appendChild(servicioDiv);
                        });

                        // Agregar el contenedor para el total
                        const totalContainer     = document.createElement('div');
                        totalContainer.className = 'total-container';
                        totalContainer.style.borderTop = '1px solid rgba(255, 255, 255, 0.2)';
                        totalContainer.innerHTML = `
                            <span class="total-label text-white">Total a agregar:</span>
                            <span class="total-value">$0</span>
                        `;
                        editarServiciosContainer.appendChild(totalContainer);

                        // Inicializar el total
                        calcularTotalEditarServicios();

                        // Prevenir la propagación del evento click en los checkboxes
                        // para evitar que el evento click del div padre se active
                        document.querySelectorAll('input[name="editar_servicios[]"]').forEach(checkbox => {
                            checkbox.addEventListener('click', function(e) {
                                e.stopPropagation();
                            });
                        });
                    } else {
                        // No hay servicios disponibles
                        editarServiciosContainer.innerHTML = `
                            <div class="alert alert-warning mb-0">
                                No hay servicios adicionales disponibles para agregar a esta cita.
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    editarServiciosContainer.innerHTML = `
                        <div class="alert alert-danger mb-0">
                            Error al cargar los servicios disponibles. Por favor, intente nuevamente.
                        </div>
                    `;
                });
        }

        // Función para calcular el total de servicios seleccionados en el modal de edición
        function calcularTotalEditarServicios() {
            const serviciosSeleccionados = document.querySelectorAll('input[name="editar_servicios[]"]:checked');
            let total = 0;

            serviciosSeleccionados.forEach(servicio => {
                total += parseFloat(servicio.dataset.valor || 0);
            });

            // Guardar el valor en el campo oculto
            editarTotalServicios.value = total;

            // Actualizar el contenedor del total
            const totalContainer = editarServiciosContainer.querySelector('.total-container');
            if (totalContainer) {
                const totalBadge = totalContainer.querySelector('span:last-child');
                if (totalBadge) {
                    totalBadge.textContent = `$${formatCurrency(total)}`;
                }
            }

            // Actualizar el total de la cita
            actualizarTotalCita();

            return total;
        }

        // Función para actualizar el total de la cita (suma de servicios actuales + nuevos servicios)
        function actualizarTotalCita() {
            // Obtener el total de servicios actuales
            const totalServiciosActuales = parseFloat(document.getElementById('editar-total-servicios-actuales').value || 0);

            // Obtener el total de nuevos servicios
            const totalNuevosServicios = parseFloat(document.getElementById('editar-total-servicios').value || 0);

            // Calcular el total combinado
            const totalCita = totalServiciosActuales + totalNuevosServicios;

            // Actualizar el elemento que muestra el total
            const totalCitaValor = document.getElementById('total-cita-valor');
            if (totalCitaValor) {
                totalCitaValor.textContent = `$${formatCurrency(totalCita)}`;
            }
        }

        // Función para actualizar la tarjeta de EmpleadoTurno en el dashboard
        // Simplemente recarga la página para mostrar los cambios actualizados
        function actualizarTarjetaEmpleadoTurno(idEmpleadoTurno, servicios, total, nombreEmpleado) {
            try {
                // Validar los parámetros de entrada
                if (!idEmpleadoTurno || !servicios || !Array.isArray(servicios) || total === undefined) {
                    console.error('Parámetros inválidos para actualizar la tarjeta de EmpleadoTurno');
                    console.error('idEmpleadoTurno:', idEmpleadoTurno);
                    console.error('servicios:', servicios);
                    console.error('total:', total);
                    return false;
                }

                // En lugar de intentar actualizar la tarjeta de forma dinámica, vamos a recargar la página
                // Esta es la solución más simple y efectiva para asegurar que la UI esté actualizada
                console.log('Actualizando la página para reflejar los cambios en la cita...');

                // Recargar la página después de un breve retraso
                setTimeout(() => {
                    window.location.reload();
                }, 500);

                return true;
            } catch (error) {
                console.error('Error al actualizar la tarjeta de EmpleadoTurno:', error);
                return false;
            }
        }

        // Función para eliminar servicios seleccionados de una cita
        function eliminarServiciosSeleccionados(idCita) {
            // Obtener los IDs de los servicios seleccionados
            const serviciosSeleccionados = [];
            document.querySelectorAll('.servicio-checkbox:checked').forEach(checkbox => {
                serviciosSeleccionados.push(checkbox.dataset.idServicio);
            });

            // Verificar que haya servicios seleccionados
            if (serviciosSeleccionados.length === 0) {
                swal({
                    title  : 'Advertencia',
                    text   : 'No hay servicios seleccionados para eliminar.',
                    icon   : 'warning',
                    buttons: {
                        confirm: {
                            text      : 'Ok',
                            value     : true,
                            visible   : true,
                            className : 'btn-warning',
                            closeModal: true
                        }
                    }
                });
                return;
            }

            // Confirmar eliminación
            swal({
                title  : '¿Eliminar servicios seleccionados?',
                text   : `¿Está seguro de eliminar ${serviciosSeleccionados.length} servicio(s) de la cita? Esta acción no se puede deshacer.`,
                icon   : 'warning',
                buttons: {
                    cancel : {
                        text      : 'Cancelar',
                        value     : null,
                        visible   : true,
                        className : 'btn-default',
                        closeModal: true
                    },
                    confirm: {
                        text      : 'Sí, eliminar',
                        value     : true,
                        visible   : true,
                        className : 'btn-danger',
                        closeModal: true
                    }
                }
            }).then((result) => {
                if (result) {
                    // Preparar los datos para enviar
                    const formData = new FormData();
                    formData.append('action', 'eliminar_servicios_cita');
                    formData.append('id_cita', idCita);
                    formData.append('servicios', JSON.stringify(serviciosSeleccionados));

                    // Deshabilitar el botón de eliminar seleccionados mientras se procesa la solicitud
                    const btnEliminarSeleccionados = document.getElementById('btn-eliminar-servicios-seleccionados');
                    btnEliminarSeleccionados.disabled = true;
                    btnEliminarSeleccionados.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Eliminando servicios...';

                    // Enviar la solicitud
                    fetch('dashboard', {
                        method: 'POST',
                        body  : formData
                    })
                        .then(response => response.json())
                        .then(data => {
                            // Restaurar el botón
                            btnEliminarSeleccionados.innerHTML = '<i class="fa fa-trash me-2"></i> Eliminar servicios seleccionados';

                            if (data.success) {
                                // Recargar los servicios de la cita en el modal
                                cargarServiciosCita(idCita);
                                // Recargar los servicios disponibles en el modal
                                cargarServiciosDisponibles(idCita);
                                // Actualizar el total de la cita en el modal
                                actualizarTotalCita();

                                // Mostrar mensaje de éxito
                                swal({
                                    title  : 'Éxito',
                                    text   : data.message + ' La página se actualizará para mostrar los cambios.',
                                    icon   : 'success',
                                    buttons: {
                                        confirm: {
                                            text      : 'Ok',
                                            value     : true,
                                            visible   : true,
                                            className : 'btn-success',
                                            closeModal: true
                                        }
                                    }
                                }).then(() => {
                                    // Actualizar la tarjeta de EmpleadoTurno en el dashboard
                                    if (data.id_empleado_turno && data.servicios && data.total) {
                                        actualizarTarjetaEmpleadoTurno(
                                            data.id_empleado_turno,
                                            data.servicios,
                                            data.total,
                                            data.nombre_empleado
                                        );
                                    } else {
                                        // Si no tenemos los datos necesarios, simplemente recargamos la página
                                        window.location.reload();
                                    }
                                });
                            } else {
                                // Mostrar mensaje de error
                                swal({
                                    title  : 'Error',
                                    text   : data.message,
                                    icon   : 'error',
                                    buttons: {
                                        confirm: {
                                            text      : 'Ok',
                                            value     : true,
                                            visible   : true,
                                            className : 'btn-danger',
                                            closeModal: true
                                        }
                                    }
                                });
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            btnEliminarSeleccionados.disabled = false;
                            btnEliminarSeleccionados.innerHTML = '<i class="fa fa-trash me-2"></i> Eliminar servicios seleccionados';

                            swal({
                                title  : 'Error',
                                text   : 'Ocurrió un error al procesar la solicitud. Por favor, intente nuevamente.',
                                icon   : 'error',
                                buttons: {
                                    confirm: {
                                        text      : 'Ok',
                                        value     : true,
                                        visible   : true,
                                        className : 'btn-danger',
                                        closeModal: true
                                    }
                                }
                            });
                        });
                }
            });
        }

        // Evento para abrir el modal de cancelación de cita
        cancelarCitaBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const idCita = this.dataset.idCita;

                // Resetear el formulario
                formCancelarCita.reset();

                // Establecer el ID de la cita
                cancelarIdCita.value = idCita;

                // Mostrar el modal
                modalCancelarCita.show();
            });
        });

        // Evento para confirmar la cancelación de la cita
        btnConfirmarCancelacion.addEventListener('click', function() {
            // Validar que se haya ingresado una razón de cancelación
            if (!razonCancelacion.value.trim()) {
                razonCancelacion.classList.add('is-invalid');
                return;
            }

            // Quitar clase de error si existe
            razonCancelacion.classList.remove('is-invalid');

            // Obtener los datos del formulario
            const idCita = cancelarIdCita.value;
            const razon = razonCancelacion.value.trim();

            // Deshabilitar el botón y mostrar indicador de carga
            btnConfirmarCancelacion.disabled = true;
            btnConfirmarCancelacion.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Procesando...';

            // Enviar solicitud AJAX para cancelar la cita
            const formData = new FormData();
            formData.append('id_cita', idCita);
            formData.append('razon_cancelacion', razon);

            fetch('cancelar-cita', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                // Cerrar el modal
                modalCancelarCita.hide();

                // Restablecer el botón
                btnConfirmarCancelacion.disabled = false;
                btnConfirmarCancelacion.innerHTML = 'Confirmar Cancelación';

                if (data.success) {
                    // Mostrar mensaje de éxito
                    swal({
                        title: 'Éxito',
                        text: data.message,
                        icon: 'success',
                        buttons: {
                            confirm: {
                                text: 'Ok',
                                value: true,
                                visible: true,
                                className: 'btn-success',
                                closeModal: true
                            }
                        }
                    }).then(() => {
                        // Recargar la página para mostrar los cambios
                        window.location.reload();
                    });
                } else {
                    // Mostrar mensaje de error
                    swal({
                        title: 'Error',
                        text: data.message,
                        icon: 'error',
                        buttons: {
                            confirm: {
                                text: 'Ok',
                                value: true,
                                visible: true,
                                className: 'btn-danger',
                                closeModal: true
                            }
                        }
                    });
                }
            })
            .catch(error => {
                // Cerrar el modal
                modalCancelarCita.hide();

                // Restablecer el botón
                btnConfirmarCancelacion.disabled = false;
                btnConfirmarCancelacion.innerHTML = 'Confirmar Cancelación';

                // Mostrar mensaje de error
                swal({
                    title: 'Error',
                    text: 'Ocurrió un error al procesar la solicitud',
                    icon: 'error',
                    buttons: {
                        confirm: {
                            text: 'Ok',
                            value: true,
                            visible: true,
                            className: 'btn-danger',
                            closeModal: true
                        }
                    }
                });
                console.error('Error:', error);
            });
        });

        // Evento para abrir el modal de edición de cita
        btnsEditarCita.forEach(btn => {
            btn.addEventListener('click', function() {
                const idCita = this.dataset.idCita;

                // Resetear el formulario
                formEditarCita.reset();

                // Establecer el ID de la cita
                editarIdCita.value = idCita;

                // Inicializar los campos de totales
                document.getElementById('editar-total-servicios').value = 0;
                document.getElementById('editar-total-servicios-actuales').value = 0;
                document.getElementById('total-cita-valor').textContent = '$0';

                // Cargar los servicios actuales de la cita
                cargarServiciosCita(idCita);

                // Cargar los servicios disponibles para agregar
                cargarServiciosDisponibles(idCita);

                // Mostrar el modal
                modalEditarCita.show();
            });
        });

        // Evento para guardar los cambios en la cita
        btnGuardarEditarCita.addEventListener('click', function() {
            const idCita = editarIdCita.value;

            // Obtener los servicios seleccionados para agregar
            const serviciosSeleccionados = [];
            document.querySelectorAll('input[name="editar_servicios[]"]:checked').forEach(servicio => {
                serviciosSeleccionados.push({
                    id   : servicio.value,
                    valor: servicio.dataset.valor
                });
            });

            // Si no hay servicios seleccionados y no hay servicios actuales, mostrar error
            if (serviciosSeleccionados.length === 0 && !document.querySelector('.appointment-service-item')) {
                swal({
                    title  : 'Error',
                    text   : 'La cita debe tener al menos un servicio',
                    icon   : 'error',
                    buttons: {
                        confirm: {
                            text      : 'Ok',
                            value     : true,
                            visible   : true,
                            className : 'btn-danger',
                            closeModal: true
                        }
                    }
                });
                return;
            }

            // Si no hay servicios seleccionados, preguntar si desea continuar sin agregar nuevos servicios
            if (serviciosSeleccionados.length === 0) {
                swal({
                    title  : 'Información',
                    text   : 'No ha seleccionado nuevos servicios para agregar. ¿Desea continuar?',
                    icon   : 'info',
                    buttons: {
                        cancel : {
                            text      : 'Cancelar',
                            value     : null,
                            visible   : true,
                            className : 'btn-default',
                            closeModal: true
                        },
                        confirm: {
                            text      : 'Sí, continuar',
                            value     : true,
                            visible   : true,
                            className : 'btn-primary',
                            closeModal: true
                        }
                    }
                }).then((result) => {
                    if (result) {
                        // Cerrar el modal
                        modalEditarCita.hide();
                    }
                });
                return;
            }

            // Preparar los datos para enviar
            const formData = new FormData();
            formData.append('action', 'agregar_servicios_cita');
            formData.append('id_cita', idCita);
            formData.append('servicios', JSON.stringify(serviciosSeleccionados));

            // Deshabilitar el botón y mostrar indicador de carga
            btnGuardarEditarCita.disabled  = true;
            btnGuardarEditarCita.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Guardando...';

            // Enviar la solicitud
            fetch('dashboard', {
                method: 'POST',
                body  : formData
            })
                .then(response => response.json())
                .then(data => {
                    // Restaurar el botón
                    btnGuardarEditarCita.disabled  = false;
                    btnGuardarEditarCita.innerHTML = 'Guardar Cambios';

                    if (data.success) {
                        // Cerrar el modal
                        modalEditarCita.hide();

                        // Mostrar mensaje de éxito
                        swal({
                            title  : 'Éxito',
                            text   : data.message + ' La página se actualizará para mostrar los cambios.',
                            icon   : 'success',
                            buttons: {
                                confirm: {
                                    text      : 'Ok',
                                    value     : true,
                                    visible   : true,
                                    className : 'btn-success',
                                    closeModal: true
                                }
                            }
                        }).then(() => {
                            // Actualizar la tarjeta de EmpleadoTurno en el dashboard
                            if (data.id_empleado_turno && data.servicios && data.total) {
                                actualizarTarjetaEmpleadoTurno(
                                    data.id_empleado_turno,
                                    data.servicios,
                                    data.total,
                                    data.nombre_empleado
                                );
                            } else {
                                // Si no tenemos los datos necesarios, simplemente recargamos la página
                                window.location.reload();
                            }
                        });
                    } else {
                        // Mostrar mensaje de error
                        swal({
                            title  : 'Error',
                            text   : data.message,
                            icon   : 'error',
                            buttons: {
                                confirm: {
                                    text      : 'Ok',
                                    value     : true,
                                    visible   : true,
                                    className : 'btn-danger',
                                    closeModal: true
                                }
                            }
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);

                    // Restaurar el botón
                    btnGuardarEditarCita.disabled  = false;
                    btnGuardarEditarCita.innerHTML = 'Guardar Cambios';

                    // Mostrar mensaje de error
                    swal({
                        title  : 'Error',
                        text   : 'Ocurrió un error al procesar la solicitud',
                        icon   : 'error',
                        buttons: {
                            confirm: {
                                text      : 'Ok',
                                value     : true,
                                visible   : true,
                                className : 'btn-danger',
                                closeModal: true
                            }
                        }
                    });
                });
        });

        // Evento para guardar la cita
        btnGuardarCita.addEventListener('click', function () {
            // Validar el formulario
            if (!validarFormulario()) {
                return;
            }

            // Obtener los servicios seleccionados
            const serviciosSeleccionados = [];
            document.querySelectorAll('input[name="servicios[]"]:checked').forEach(servicio => {
                serviciosSeleccionados.push({
                    id   : servicio.value,
                    valor: servicio.dataset.valor
                });
            });

            // Preparar los datos para enviar
            const formData = new FormData();
            formData.append('action', 'crear_cita');
            formData.append('id_empleado_turno', selectTurnoEmpleado.value);
            formData.append('servicios', JSON.stringify(serviciosSeleccionados));

            // Si la cita se está creando desde una cita programada, incluir el ID
            if (modalNuevaCita._citaProgramadaId) {
                formData.append('id_cita_programada', modalNuevaCita._citaProgramadaId);
            }

            // Deshabilitar el botón y mostrar indicador de carga
            btnGuardarCita.disabled  = true;
            btnGuardarCita.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Guardando...';

            // Enviar la solicitud
            fetch('dashboard', {
                method: 'POST',
                body  : formData
            })
                .then(response => response.json())
                .then(data => {
                    // Restaurar el botón
                    btnGuardarCita.disabled  = false;
                    btnGuardarCita.innerHTML = 'Guardar';

                    if (data.success) {
                        // Cerrar el modal
                        modalNuevaCita.hide();

                        // Mostrar mensaje de éxito
                        swal({
                            title  : 'Éxito',
                            text   : data.message,
                            icon   : 'success',
                            buttons: {
                                confirm: {
                                    text      : 'Ok',
                                    value     : true,
                                    visible   : true,
                                    className : 'btn-success',
                                    closeModal: true
                                }
                            }
                        }).then(() => {
                            // Recargar la página para mostrar los cambios
                            window.location.reload();
                        });
                    } else {
                        // Mostrar mensaje de error
                        swal({
                            title  : 'Error',
                            text   : data.message,
                            icon   : 'error',
                            buttons: {
                                confirm: {
                                    text      : 'Ok',
                                    value     : true,
                                    visible   : true,
                                    className : 'btn-danger',
                                    closeModal: true
                                }
                            }
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);

                    // Restaurar el botón
                    btnGuardarCita.disabled  = false;
                    btnGuardarCita.innerHTML = 'Guardar';

                    // Mostrar mensaje de error
                    swal({
                        title  : 'Error',
                        text   : 'Ocurrió un error al procesar la solicitud',
                        icon   : 'error',
                        buttons: {
                            confirm: {
                                text      : 'Ok',
                                value     : true,
                                visible   : true,
                                className : 'btn-danger',
                                closeModal: true
                            }
                        }
                    });
                });
        });

        // Evento para cerrar caja
        btnCerrarCaja.addEventListener('click', function() {
            // Confirmar la acción
            swal({
                title: '¿Cerrar caja?',
                text: 'Solo se verificarán las citas del centro de costo "<?php echo htmlspecialchars($selected_centro_costo_nombre ?? 'seleccionado'); ?>". Las citas de otros centros de costo no se verán afectadas. Los siguientes tipos de documentos incluidos en este cierre serán bloqueados y no podrán modificarse después de esta operación: ventas, gastos fijos, gastos operativos, y ordenes de compra.',
                icon: 'warning',
                buttons: {
                    cancel: {
                        text: 'Cancelar',
                        value: null,
                        visible: true,
                        className: 'btn-default',
                        closeModal: true
                    },
                    confirm: {
                        text: 'Verificar y continuar',
                        value: true,
                        visible: true,
                        className: 'btn-warning',
                        closeModal: true
                    }
                }
            }).then((result) => {
                if (result) {
                    // Realizar la validación de citas y turnos activos
                    validarCitasActivasParaCierre();
                }
            });
        });

        // Función para validar citas y turnos activos antes del cierre de caja
        function validarCitasActivasParaCierre() {
            const formData = new FormData();
            formData.append('action', 'validar_cierre_caja');

            fetch('dashboard', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const nombreCentroCosto = data.nombre_centro_costo || 'seleccionado';

                    // Verificar si hay citas activas
                    if (data.citas_activas > 0) {
                        // Hay citas activas, mostrar error
                        showSweetAlertError(
                            'No se puede cerrar la caja',
                            `Hay ${data.citas_activas} cita(s) en progreso en el centro de costo "${nombreCentroCosto}". Debe finalizar todas las citas antes de cerrar la caja.`
                        );
                    }
                    // Verificar si hay turnos activos (solo si no hay citas activas)
                    else if (data.turnos_activos > 0) {
                        // Hay turnos activos, mostrar error
                        showSweetAlertError(
                            'No se puede cerrar la caja',
                            `Hay ${data.turnos_activos} turno(s) activo(s) en el centro de costo "${nombreCentroCosto}". Debe finalizar todos los turnos antes de cerrar la caja.`
                        );
                    }
                    // No hay citas ni turnos activos
                    else {
                        // No hay citas ni turnos activos, mostrar éxito con redirección
                        swal({
                            title: 'Validación exitosa',
                            text: `No hay citas ni turnos activos en el centro de costo "${nombreCentroCosto}". Se puede proceder con el cierre de caja.`,
                            icon: 'success',
                            buttons: false,
                            closeOnClickOutside: false,
                            closeOnEsc: false,
                            content: {
                                element: "div",
                                attributes: {
                                    innerHTML: `
                                        <div class="text-center mt-3">
                                            <div class="spinner-border text-primary mb-2" role="status">
                                                <span class="visually-hidden">Cargando...</span>
                                            </div>
                                            <p class="text-muted">Redireccionando...</p>
                                        </div>
                                    `
                                }
                            }
                        });

                        // Redireccionar después de 2.5 segundos
                        setTimeout(function() {
                            window.location.href = 'cerrar-caja';
                        }, 2500);
                    }
                } else {
                    // Error en la validación
                    showSweetAlertError('Error', data.message || 'Error al validar el estado para cierre de caja.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showSweetAlertError('Error', 'Ocurrió un error al procesar la solicitud.');
            });
        }
    });
</script>

<!-- Include Bootstrap Datepicker JS -->
<script src="<?php echo RUTA ?>resources/adm_assets/plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>

<?php #endregion JS ?>



</body>
</html>